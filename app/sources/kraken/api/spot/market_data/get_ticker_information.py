# Retrieve ticker data across all markets.
# Endpoint does not require authentication,
# but has utility functions for authentication.

"""Kraken API Client
Documentation: https://docs.kraken.com/api/docs/rest-api/get-ohlc-data

This module provides functionality to interact with the Kraken cryptocurrency exchange API.
It includes methods for retrieving OHLC (Open, High, Low, Close) data and handling authentication.

Endpoint:
    GET /0/public/OHLC
    https://api.kraken.com/0/public/OHLC

Description:
    Retrieve OHLC market data for a given trading pair.
    The endpoint does not require authentication but supports it for utility functions.
    Returns up to 720 of the most recent entries; older data cannot be retrieved.
    The last entry in the OHLC array is for the current, not-yet-committed timeframe and is always present.

Features:
- Retrieve OHLC data for cryptocurrency pairs
- Support for public API endpoints
- Authentication utilities for private API access
- Request signing and nonce generation

Example Usage:
```python
# Retrieve 5m OHLC data for BTC/USD
response = request(
    method="GET",
    path="/0/public/OHLC",
    query={
        "pair": "BTC/USD",
        "interval": 5,
    },
    environment="https://api.kraken.com",
)
data = response.read().decode()
```

Response Schema:
- result: object
  - last: integer - ID to use when polling for new data
  - [pair_name]: array of arrays with the following structure:
    [time, open, high, low, close, vwap, volume, count]
    - time: integer - Unix timestamp
    - open: string - Opening price
    - high: string - Highest price
    - low: string - Lowest price
    - close: string - Closing price
    - vwap: string - Volume weighted average price
    - volume: string - Volume
    - count: integer - Number of trades
- error: array of strings - List of error messages, if any

Notes:
- The 'since' parameter can be used to poll for new OHLC data since a given ID
- The endpoint supports intervals in minutes: 1, 5, 15, 30, 60, 240, 1440, 10080, 21600
"""

import http.client
import urllib.request
import urllib.parse
import hashlib
import hmac
import base64
import json
import time


def main():
    response = request(
            method = "GET",
            path = "/0/public/Ticker",
            environment = "https://api.kraken.com",
    )
    print(response.read().decode())


def request(method: str = "GET", path: str = "", query: dict | None = None, body: dict | None = None,
            public_key: str = "", private_key: str = "", environment: str = "") -> http.client.HTTPResponse:
    url = environment + path
    query_str = ""
    if query is not None and len(query) > 0:
        query_str = urllib.parse.urlencode(query)
        url += "?" + query_str
    nonce = ""
    if len(public_key) > 0:
        if body is None:
            body = {}
        nonce = body.get("nonce")
        if nonce is None:
            nonce = get_nonce()
            body["nonce"] = nonce
    headers = {}
    body_str = ""
    if body is not None and len(body) > 0:
        body_str = json.dumps(body)
        headers["Content-Type"] = "application/json"
    if len(public_key) > 0:
        headers["API-Key"] = public_key
        headers["API-Sign"] = get_signature(private_key, query_str + body_str, nonce, path)
    req = urllib.request.Request(
            method = method,
            url = url,
            data = body_str.encode(),
            headers = headers,
    )
    return urllib.request.urlopen(req)


def get_nonce() -> str:
    return str(int(time.time() * 1000))


def get_signature(private_key: str, data: str, nonce: str, path: str) -> str:
    return sign(
            private_key = private_key,
            message = path.encode() + hashlib.sha256(
                    (nonce + data)
                    .encode()
            ).digest()
    )


def sign(private_key: str, message: bytes) -> str:
    return base64.b64encode(
            hmac.new(
                    key = base64.b64decode(private_key),
                    msg = message,
                    digestmod = hashlib.sha512,
            ).digest()
    ).decode()


if __name__ == "__main__":
    main()
